<!-- 桥隧健康度 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>桥隧健康度</span>
        <span class="header-subtitle">BRIDGE AND TUNNEL HEALTH</span>
      </div>
    </template>

    <template #content>
      <CEcharts ref="chartRef" :option="option" />
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()

// 桥隧健康度数据
const healthData = [
  { name: 'I基本完好', value: 31, color: '#00FF7F' },
  { name: 'II轻微异常', value: 28, color: '#4169E1' },
  { name: 'III中等异常', value: 18, color: '#1E90FF' },
  { name: 'IV严重异常', value: 15, color: '#00BFFF' }
]

// 创建带间隔的数据
const createPieDataWithGaps = () => {
  const data = []
  healthData.forEach((item, index) => {
    // 添加实际数据
    data.push({
      name: item.name,
      value: item.value,
      itemStyle: {
        color: item.color,
        borderColor: '#FFFFFF',
        borderWidth: 4
      }
    })
    // 添加间隔（除了最后一个）
    if (index < healthData.length - 1) {
      data.push({
        name: 'gap',
        value: 1,
        itemStyle: {
          color: 'rgba(0,0,0,0)',
          borderColor: 'transparent',
          borderWidth: 0
        }
      })
    }
  })
  return data
}

const createHealthChart = () => {
  return {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        if (params.name === 'gap') return ''
        return `${params.seriesName}<br/>${params.name}: ${params.value} (${params.percent}%)`
      }
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 15,
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      formatter: function (name) {
        const item = healthData.find(d => d.name === name)
        return `${name}    ${item.value}`
      },
      data: healthData.map(item => item.name)
    },
    graphic: [
      {
        type: 'text',
        left: '15%',
        top: 'center',
        style: {
          text: '16.3%\nIV严重异常',
          textAlign: 'center',
          fill: '#fff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      }
    ],
    series: [
      {
        name: '桥隧健康度',
        type: 'pie',
        radius: ['40%', '61%'],
        center: ['25%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: createPieDataWithGaps()
      }
    ]
  }
}



onMounted(() => {
  option.value = createHealthChart()
})
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}
</style>
